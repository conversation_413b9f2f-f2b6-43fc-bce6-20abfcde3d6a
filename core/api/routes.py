from flask import jsonify, request
from core.api import api_bp
from core.utils.auth_utils import auth_required
import sys
import os

# Add Backend directory to Python path
sys.path.append(os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 'Backend'))

from org_data import get_organization_data, save_organization_data
from archetype import get_brand_archetype_analysis, save_archetype_data
from brand_guidelines import get_brand_guidelines, save_brand_data
from image_scraper import scrape_images, get_image_statistics, validate_url, estimate_scraping_time, get_supported_image_types
from product_details import get_product_details, save_product_data, load_product_data, get_all_products
from template_creator import create_templates, load_user_journey, get_template_statistics, load_communication_settings


@api_bp.route('/hello', methods=['GET'])
@auth_required
def hello():
    return jsonify({'hello': 'world'})


@api_bp.route('/organization-data', methods=['GET'])
# @auth_required
def analyze_organization():
    """
    POST API endpoint to analyze organization data from a website URL.

    Expected JSON payload:
    {
        "org_url": "https://example.com",
        "save_data": true  // optional, defaults to true
    }

    Returns:
    {
        "success": true,
        "data": {
            "Domain": "...",
            "WhatWeDo": "...",
            "AboutUs": "...",
            "Class": "...",
            "url": "..."
        },
        "saved": true  // indicates if data was saved to storage
    }
    """
    try:
        # Get JSON data from request
        # data = request.get_json()

        # if not data:
        #     return jsonify({
        #         "success": False,
        #         "error": "No JSON data provided"
        #     }), 400

        # Validate required fields
        # org_url = data.get('org_url')
        # if not org_url:
        #     return jsonify({
        #         "success": False,
        #         "error": "Missing required field: org_url"
        #     }), 400

        # # Validate URL format (basic validation)
        # if not org_url.startswith(('http://', 'https://')):
        #     return jsonify({
        #         "success": False,
        #         "error": "Invalid URL format. URL must start with http:// or https://"
        #     }), 400

        # Get save_data flag (defaults to True)
        # save_data = data.get('save_data', True)
        org_url = 'https://www.analyticsvidhya.com/'

        # Process organization data
        print("111111", org_url)
        org_data = get_organization_data(org_url)

        # Check if analysis was successful
        if org_data.get('Domain') == 'Error':
            return jsonify({
                "success": False,
                "error": "Failed to analyze organization data",
                "details": org_data.get('AboutUs', 'Unknown error occurred'),
                "data": org_data
            }), 500

        # Save data if requested
        # saved = False
        # if save_data:
            # saved = save_organization_data(org_data, org_url)

        # Return successful response
        return jsonify({
            "success": True,
            "data": org_data,
            # "saved": saved,
            "message": "Organization data analyzed successfully"
        }), 200

    except Exception as e:
        return jsonify({
            "success": False,
            "error": "Internal server error",
            "details": str(e)
        }), 500


@api_bp.route('/brand-archetype-analysis', methods=['POST'])
# @auth_required
def analyze_brand_archetype():
    """
    POST API endpoint to analyze brand archetype from a website URL.

    Expected JSON payload:
    {
        "org_url": "https://example.com",
        "save_data": true  // optional, defaults to true
    }

    Returns:
    {
        "success": true,
        "data": {
            "mission": "...",
            "vision": "...",
            "tone_of_voice": "...",
            "brand_personality_traits": [...],
            "core_values": [...],
            "archetype_scores": {
                "Hero": {"score": 85.5, "reasoning": "..."},
                "Sage": {"score": 72.3, "reasoning": "..."},
                ...
            },
            "primary_archetype": "Hero",
            "primary_reasoning": "...",
            "organization_url": "..."
        },
        "saved": true
    }
    """
    try:
        # Get JSON data from request
        data = request.get_json()

        if not data:
            return jsonify({
                "success": False,
                "error": "No JSON data provided"
            }), 400

        # Validate required fields
        org_url = data.get('org_url')
        if not org_url:
            return jsonify({
                "success": False,
                "error": "Missing required field: org_url"
            }), 400

        # Validate URL format (basic validation)
        if not org_url.startswith(('http://', 'https://')):
            return jsonify({
                "success": False,
                "error": "Invalid URL format. URL must start with http:// or https://"
            }), 400

        # Get save_data flag (defaults to True)
        save_data = data.get('save_data', True)

        # Process brand archetype analysis
        archetype_analysis = get_brand_archetype_analysis(org_url)

        # Check if analysis was successful
        if archetype_analysis.get('error'):
            return jsonify({
                "success": False,
                "error": "Failed to analyze brand archetype",
                "details": archetype_analysis.get('error', 'Unknown error occurred'),
                "data": archetype_analysis
            }), 500

        # Check for analysis errors based on content
        if (archetype_analysis.get('mission') == 'Error occurred during analysis' or
            archetype_analysis.get('primary_archetype') == 'Unknown'):
            return jsonify({
                "success": False,
                "error": "Failed to complete brand archetype analysis",
                "details": "Analysis returned incomplete or error results",
                "data": archetype_analysis
            }), 500

        # Save data if requested
        saved = False
        if save_data:
            saved = save_archetype_data(archetype_analysis, org_url)

        # Return successful response
        return jsonify({
            "success": True,
            "data": archetype_analysis,
            "saved": saved,
            "message": "Brand archetype analysis completed successfully"
        }), 200

    except Exception as e:
        return jsonify({
            "success": False,
            "error": "Internal server error",
            "details": str(e)
        }), 500


@api_bp.route('/brand-guidelines', methods=['POST'])
@auth_required
def analyze_brand_guidelines():
    """
    POST API endpoint to analyze brand guidelines from a website URL.

    Expected JSON payload:
    {
        "org_url": "https://example.com",
        "save_data": true  // optional, defaults to true
    }

    Returns:
    {
        "success": true,
        "data": {
            "primary_color": "#1E88E5",
            "primary_color_reasoning": "Used prominently in header and main navigation",
            "secondary_color": "#FFC107",
            "secondary_color_reasoning": "Used for secondary buttons and highlights",
            "accent_color": "#4CAF50",
            "accent_color_reasoning": "Used for call-to-action elements and success states",
            "neutral_color": "#F5F5F5",
            "neutral_color_reasoning": "Used for backgrounds and subtle elements",
            "background_color": "#FFFFFF",
            "background_color_reasoning": "Main content area background",
            "text_color": "#212121",
            "text_color_reasoning": "Primary text color for readability",
            "has_gradient": true,
            "gradient_colors": "linear-gradient(45deg, #1E88E5, #42A5F5)",
            "gradient_direction": "45deg",
            "cta_type": "Button",
            "cta_size": "Medium",
            "button_style": "Rounded",
            "border_radius": "8px",
            "font": "Roboto",
            "font_size": "16px",
            "font_weight": "Normal",
            "mission": "...",
            "vision": "...",
            "tone_of_voice": "...",
            "organization_url": "...",
            "analysis_method": "screenshot_enhanced",
            "model_used": "gpt-4-vision-preview"
        },
        "saved": true
    }
    """
    try:
        # Get JSON data from request
        data = request.get_json()

        if not data:
            return jsonify({
                "success": False,
                "error": "No JSON data provided"
            }), 400

        # Validate required fields
        org_url = data.get('org_url')
        if not org_url:
            return jsonify({
                "success": False,
                "error": "Missing required field: org_url"
            }), 400

        # Validate URL format (basic validation)
        if not org_url.startswith(('http://', 'https://')):
            return jsonify({
                "success": False,
                "error": "Invalid URL format. URL must start with http:// or https://"
            }), 400

        # Get save_data flag (defaults to True)
        save_data = data.get('save_data', True)

        # Process brand guidelines analysis
        brand_guidelines = get_brand_guidelines(org_url)

        # Check if analysis was successful
        if brand_guidelines.get('error'):
            return jsonify({
                "success": False,
                "error": "Failed to analyze brand guidelines",
                "details": brand_guidelines.get('error', 'Unknown error occurred'),
                "data": brand_guidelines
            }), 500

        # Check for analysis errors based on content
        if (brand_guidelines.get('primary_color') == '#default' or
            brand_guidelines.get('primary_color_reasoning') == 'Analysis failed - using default value'):
            return jsonify({
                "success": False,
                "error": "Failed to complete brand guidelines analysis",
                "details": "Analysis returned incomplete or error results",
                "data": brand_guidelines
            }), 500

        # Save data if requested
        saved = False
        if save_data:
            try:
                save_brand_data(brand_guidelines)
                saved = True
            except Exception as save_error:
                # Don't fail the entire request if saving fails
                saved = False

        # Return successful response
        return jsonify({
            "success": True,
            "data": brand_guidelines,
            "saved": saved,
            "message": "Brand guidelines analysis completed successfully"
        }), 200

    except Exception as e:
        return jsonify({
            "success": False,
            "error": "Internal server error",
            "details": str(e)
        }), 500


@api_bp.route('/scrape-images', methods=['POST'])
@auth_required
def scrape_website_images():
    """
    POST API endpoint to scrape images from a website URL.

    Expected JSON payload:
    {
        "url": "https://example.com",
        "min_pixels": 100,  // optional, defaults to 100
        "num_images": 10,   // optional, no limit if not specified
        "image_types": ["jpg", "jpeg", "png", "webp"],  // optional, defaults to all supported types
        "usage": "Product Images"  // optional, defaults to "All Products"
    }

    Returns:
    {
        "success": true,
        "data": {
            "total_images": 5,
            "images_data": [
                {
                    "id": "uuid-string",
                    "filename": "scraped_image_1.jpg",
                    "original_filename": "product.jpg",
                    "name": "product.jpg",
                    "upload_date": "2024-07-25T10:30:00",
                    "categories": [],
                    "usage": "Product Images",
                    "notes": "",
                    "width": 800,
                    "height": 600,
                    "thumbnail": "base64-encoded-thumbnail",
                    "source": "web_scraping"
                }
            ],
            "skipped_images": 2,
            "errors": [],
            "url": "https://example.com"
        },
        "message": "Successfully scraped 5 images"
    }
    """
    try:
        # Get JSON data from request
        data = request.get_json()

        if not data:
            return jsonify({
                "success": False,
                "error": "No JSON data provided"
            }), 400

        # Validate required fields
        url = data.get('url')
        if not url:
            return jsonify({
                "success": False,
                "error": "Missing required field: url"
            }), 400

        # Validate URL format (basic validation)
        if not url.startswith(('http://', 'https://')):
            return jsonify({
                "success": False,
                "error": "Invalid URL format. URL must start with http:// or https://"
            }), 400

        # Get optional parameters with defaults
        min_pixels = data.get('min_pixels', 100)
        num_images = data.get('num_images')  # None means no limit
        image_types = data.get('image_types')  # None means use defaults
        usage = data.get('usage', 'All Products')

        # Validate min_pixels
        if not isinstance(min_pixels, int) or min_pixels < 1:
            return jsonify({
                "success": False,
                "error": "min_pixels must be a positive integer"
            }), 400

        # Validate num_images if provided
        if num_images is not None and (not isinstance(num_images, int) or num_images < 1):
            return jsonify({
                "success": False,
                "error": "num_images must be a positive integer"
            }), 400

        # Validate image_types if provided
        if image_types is not None:
            if not isinstance(image_types, list) or not all(isinstance(t, str) for t in image_types):
                return jsonify({
                    "success": False,
                    "error": "image_types must be a list of strings"
                }), 400

            # Check against supported types
            supported_types = get_supported_image_types()
            invalid_types = [t for t in image_types if t.lower() not in supported_types]
            if invalid_types:
                return jsonify({
                    "success": False,
                    "error": f"Unsupported image types: {invalid_types}. Supported types: {supported_types}"
                }), 400

        # Validate URL accessibility (optional check)
        if not validate_url(url):
            return jsonify({
                "success": False,
                "error": "URL is not accessible or does not respond"
            }), 400

        # Process image scraping
        scraping_results = scrape_images(
            url=url,
            min_pixels=min_pixels,
            num_images=num_images,
            image_types=image_types,
            usage=usage
        )

        # Check if scraping was successful
        if not scraping_results.get('success', False):
            errors = scraping_results.get('errors', ['Unknown error occurred'])
            return jsonify({
                "success": False,
                "error": "Failed to scrape images",
                "details": errors,
                "data": scraping_results
            }), 500

        # Return successful response
        total_images = scraping_results.get('total_images', 0)
        message = f"Successfully scraped {total_images} image{'s' if total_images != 1 else ''}"

        return jsonify({
            "success": True,
            "data": scraping_results,
            "message": message
        }), 200

    except Exception as e:
        return jsonify({
            "success": False,
            "error": "Internal server error",
            "details": str(e)
        }), 500


@api_bp.route('/image-statistics', methods=['GET'])
@auth_required
def get_scraped_image_statistics():
    """
    GET API endpoint to retrieve statistics about scraped images.

    Query parameters:
    - usage_filter (optional): Filter statistics by usage category

    Returns:
    {
        "success": true,
        "data": {
            "total_images": 25,
            "usage_categories": {
                "Product Images": 15,
                "All Products": 10
            },
            "image_types": {
                "jpg": 12,
                "png": 8,
                "webp": 5
            },
            "size_distribution": {
                "small": 5,    // < 500px
                "medium": 12,  // 500-1000px
                "large": 8     // > 1000px
            },
            "source_distribution": {
                "web_scraping": 20,
                "manual_upload": 5,
                "other": 0
            }
        }
    }
    """
    try:
        # Get optional usage filter from query parameters
        usage_filter = request.args.get('usage_filter')

        # Get image statistics
        stats = get_image_statistics(usage_filter)

        # Check if there was an error in getting statistics
        if 'error' in stats:
            return jsonify({
                "success": False,
                "error": "Failed to retrieve image statistics",
                "details": stats['error']
            }), 500

        return jsonify({
            "success": True,
            "data": stats,
            "message": "Image statistics retrieved successfully"
        }), 200

    except Exception as e:
        return jsonify({
            "success": False,
            "error": "Internal server error",
            "details": str(e)
        }), 500


@api_bp.route('/scraping-info', methods=['GET'])
@auth_required
def get_scraping_info():
    """
    GET API endpoint to retrieve information about image scraping capabilities.

    Returns:
    {
        "success": true,
        "data": {
            "supported_image_types": ["jpg", "jpeg", "png", "webp", "gif"],
            "default_min_pixels": 100,
            "default_usage": "All Products"
        }
    }
    """
    try:
        return jsonify({
            "success": True,
            "data": {
                "supported_image_types": get_supported_image_types(),
                "default_min_pixels": 100,
                "default_usage": "All Products"
            },
            "message": "Scraping information retrieved successfully"
        }), 200

    except Exception as e:
        return jsonify({
            "success": False,
            "error": "Internal server error",
            "details": str(e)
        }), 500


@api_bp.route('/estimate-scraping', methods=['POST'])
@auth_required
def estimate_image_scraping():
    """
    POST API endpoint to estimate scraping time and image count for a URL.

    Expected JSON payload:
    {
        "url": "https://example.com"
    }

    Returns:
    {
        "success": true,
        "data": {
            "estimated_images": 25,
            "estimated_time_seconds": 50,
            "estimated_time_minutes": 0.8,
            "url": "https://example.com"
        }
    }
    """
    try:
        # Get JSON data from request
        data = request.get_json()

        if not data:
            return jsonify({
                "success": False,
                "error": "No JSON data provided"
            }), 400

        # Validate required fields
        url = data.get('url')
        if not url:
            return jsonify({
                "success": False,
                "error": "Missing required field: url"
            }), 400

        # Validate URL format
        if not url.startswith(('http://', 'https://')):
            return jsonify({
                "success": False,
                "error": "Invalid URL format. URL must start with http:// or https://"
            }), 400

        # Get estimation
        estimation = estimate_scraping_time(url)

        # Check if estimation was successful
        if not estimation.get('success', False):
            return jsonify({
                "success": False,
                "error": "Failed to estimate scraping time",
                "details": estimation.get('error', 'Unknown error occurred'),
                "data": estimation
            }), 500

        return jsonify({
            "success": True,
            "data": estimation,
            "message": "Scraping estimation completed successfully"
        }), 200

    except Exception as e:
        return jsonify({
            "success": False,
            "error": "Internal server error",
            "details": str(e)
        }), 500


@api_bp.route('/product-details', methods=['POST'])
@auth_required
def analyze_product_details():
    """
    POST API endpoint to analyze product details from URL(s).

    Expected JSON payload:
    {
        "product_urls": "https://example.com/product" or ["url1", "url2"],
        "organization_url": "https://company.com",  // optional
        "save_data": true  // optional, defaults to true
    }

    Returns:
    {
        "success": true,
        "data": {
            "Product_Name": "Amazing Product",
            "Company_Name": "Tech Corp",
            "Type_of_Product": "Software",
            "Product_Features": ["Feature 1", "Feature 2"],
            "Product_Summary": "Detailed product description...",
            "Product_URL": "https://example.com/product",
            "Company_URL": "https://company.com",
            "organization_url": "https://company.com",
            "Priority": 1,
            "Collaterals": []
        },
        "saved": true
    }
    """
    try:
        # Get JSON data from request
        data = request.get_json()

        if not data:
            return jsonify({
                "success": False,
                "error": "No JSON data provided"
            }), 400

        # Validate required fields
        product_urls = data.get('product_urls')
        if not product_urls:
            return jsonify({
                "success": False,
                "error": "Missing required field: product_urls"
            }), 400

        # Validate product_urls format
        if isinstance(product_urls, str):
            if not product_urls.startswith(('http://', 'https://')):
                return jsonify({
                    "success": False,
                    "error": "Invalid URL format. URL must start with http:// or https://"
                }), 400
        elif isinstance(product_urls, list):
            if not product_urls:
                return jsonify({
                    "success": False,
                    "error": "product_urls list cannot be empty"
                }), 400
            for url in product_urls:
                if not isinstance(url, str) or not url.startswith(('http://', 'https://')):
                    return jsonify({
                        "success": False,
                        "error": f"Invalid URL format: {url}. All URLs must start with http:// or https://"
                    }), 400
        else:
            return jsonify({
                "success": False,
                "error": "product_urls must be a string or list of strings"
            }), 400

        # Get optional parameters
        organization_url = data.get('organization_url')
        save_data = data.get('save_data', True)

        # Validate organization_url if provided
        if organization_url and not organization_url.startswith(('http://', 'https://')):
            return jsonify({
                "success": False,
                "error": "Invalid organization_url format. URL must start with http:// or https://"
            }), 400

        # Process product details
        product_details = get_product_details(product_urls, organization_url)

        # Check if analysis was successful
        is_list = isinstance(product_details, list)
        if is_list:
            # Check for errors in any product
            for product in product_details:
                if product.get('Product_Name') == 'Error':
                    return jsonify({
                        "success": False,
                        "error": "Failed to analyze one or more products",
                        "details": "Some products could not be processed",
                        "data": product_details
                    }), 500
        else:
            # Single product check
            if product_details.get('Product_Name') == 'Error':
                return jsonify({
                    "success": False,
                    "error": "Failed to analyze product details",
                    "details": product_details.get('Product_Summary', 'Unknown error occurred'),
                    "data": product_details
                }), 500

        # Save data if requested
        saved = False
        if save_data:
            saved = save_product_data(product_details)

        # Return successful response
        count = len(product_details) if is_list else 1
        message = f"Successfully analyzed {count} product{'s' if count != 1 else ''}"

        return jsonify({
            "success": True,
            "data": product_details,
            "saved": saved,
            "message": message
        }), 200

    except Exception as e:
        return jsonify({
            "success": False,
            "error": "Internal server error",
            "details": str(e)
        }), 500


@api_bp.route('/create-templates', methods=['POST'])
@auth_required
def create_marketing_templates():
    """
    POST API endpoint to create marketing templates for product stages.

    Expected JSON payload:
    {
        "product_stages": ["Awareness", "Consideration", "Decision"],
        "stage_details": {
            "Awareness": {
                "description": "Introduce the product to potential customers",
                "current_stage": "Unknown",
                "goal_stage": "Aware"
            }
        },
        "num_templates_per_stage": 2,
        "include_emojis": {
            "use_emojis_subject": true,
            "use_emojis_body": false
        },
        "product_data": {
            "Product_Name": "Amazing Product",
            "Company_Name": "Tech Corp",
            "Product_Features": ["Feature 1", "Feature 2"],
            "Product_Summary": "Product description...",
            "Product_URL": "https://example.com/product"
        },
        "organization_url": "https://company.com"  // optional
    }

    Returns:
    {
        "success": true,
        "data": {
            "total_templates": 6,
            "stage_results": {
                "Awareness": {
                    "success": true,
                    "templates_generated": 2,
                    "message": "Templates generated successfully",
                    "templates": [...]
                }
            },
            "errors": []
        }
    }
    """
    try:
        # Get JSON data from request
        data = request.get_json()

        if not data:
            return jsonify({
                "success": False,
                "error": "No JSON data provided"
            }), 400

        # Validate required fields
        product_stages = data.get('product_stages')
        if not product_stages:
            return jsonify({
                "success": False,
                "error": "Missing required field: product_stages"
            }), 400

        if not isinstance(product_stages, list) or not product_stages:
            return jsonify({
                "success": False,
                "error": "product_stages must be a non-empty list"
            }), 400

        stage_details = data.get('stage_details')
        if not stage_details:
            return jsonify({
                "success": False,
                "error": "Missing required field: stage_details"
            }), 400

        if not isinstance(stage_details, dict):
            return jsonify({
                "success": False,
                "error": "stage_details must be a dictionary"
            }), 400

        num_templates_per_stage = data.get('num_templates_per_stage')
        if not num_templates_per_stage:
            return jsonify({
                "success": False,
                "error": "Missing required field: num_templates_per_stage"
            }), 400

        if not isinstance(num_templates_per_stage, int) or num_templates_per_stage < 1:
            return jsonify({
                "success": False,
                "error": "num_templates_per_stage must be a positive integer"
            }), 400

        include_emojis = data.get('include_emojis')
        if not include_emojis:
            return jsonify({
                "success": False,
                "error": "Missing required field: include_emojis"
            }), 400

        if not isinstance(include_emojis, dict):
            return jsonify({
                "success": False,
                "error": "include_emojis must be a dictionary"
            }), 400

        product_data = data.get('product_data')
        if not product_data:
            return jsonify({
                "success": False,
                "error": "Missing required field: product_data"
            }), 400

        if not isinstance(product_data, dict):
            return jsonify({
                "success": False,
                "error": "product_data must be a dictionary"
            }), 400

        # Validate that all stages have details
        missing_stages = [stage for stage in product_stages if stage not in stage_details]
        if missing_stages:
            return jsonify({
                "success": False,
                "error": f"Missing stage details for: {missing_stages}"
            }), 400

        # Get optional parameters
        organization_url = data.get('organization_url')

        # Validate organization_url if provided
        if organization_url and not organization_url.startswith(('http://', 'https://')):
            return jsonify({
                "success": False,
                "error": "Invalid organization_url format. URL must start with http:// or https://"
            }), 400

        # Validate emoji settings
        required_emoji_keys = ['use_emojis_subject', 'use_emojis_body']
        for key in required_emoji_keys:
            if key not in include_emojis:
                return jsonify({
                    "success": False,
                    "error": f"Missing emoji setting: {key}"
                }), 400
            if not isinstance(include_emojis[key], bool):
                return jsonify({
                    "success": False,
                    "error": f"Emoji setting {key} must be a boolean"
                }), 400

        # Process template creation
        template_results = create_templates(
            product_stages=product_stages,
            stage_details=stage_details,
            num_templates_per_stage=num_templates_per_stage,
            include_emojis=include_emojis,
            product_data=product_data,
            organization_url=organization_url
        )

        # Check if template creation was successful
        if not template_results.get('success', False):
            errors = template_results.get('errors', ['Unknown error occurred'])
            return jsonify({
                "success": False,
                "error": "Failed to create templates",
                "details": errors,
                "data": template_results
            }), 500

        # Return successful response
        total_templates = template_results.get('total_templates', 0)
        message = f"Successfully created {total_templates} template{'s' if total_templates != 1 else ''}"

        return jsonify({
            "success": True,
            "data": template_results,
            "message": message
        }), 200

    except Exception as e:
        return jsonify({
            "success": False,
            "error": "Internal server error",
            "details": str(e)
        }), 500


@api_bp.route('/products', methods=['GET'])
@auth_required
def get_products():
    """
    GET API endpoint to retrieve stored product data.

    Query parameters:
    - organization_url (optional): Filter by organization URL
    - product_name (optional): Get specific product by name

    Returns:
    {
        "success": true,
        "data": [...] or {...},
        "count": 5
    }
    """
    try:
        # Get query parameters
        organization_url = request.args.get('organization_url')
        product_name = request.args.get('product_name')

        # Load product data
        if product_name:
            products = load_product_data(product_name=product_name)
            count = 1 if products else 0
        elif organization_url:
            products = get_all_products(organization_url=organization_url, filter_by_org=True)
            count = len(products) if isinstance(products, list) else 0
        else:
            products = get_all_products()
            count = len(products) if isinstance(products, list) else 0

        return jsonify({
            "success": True,
            "data": products,
            "count": count,
            "message": f"Retrieved {count} product{'s' if count != 1 else ''}"
        }), 200

    except Exception as e:
        return jsonify({
            "success": False,
            "error": "Internal server error",
            "details": str(e)
        }), 500


@api_bp.route('/template-statistics', methods=['GET'])
@auth_required
def get_template_stats():
    """
    GET API endpoint to retrieve template statistics.

    Query parameters:
    - organization_url (optional): Filter by organization URL

    Returns:
    {
        "success": true,
        "data": {
            "total_templates": 15,
            "stages": {
                "Awareness": 5,
                "Consideration": 5,
                "Decision": 5
            },
            "emoji_usage": {
                "with_emojis": 8,
                "without_emojis": 7
            },
            "channels": {
                "Email": 15
            }
        }
    }
    """
    try:
        # Get query parameters
        organization_url = request.args.get('organization_url')

        # Get template statistics
        stats = get_template_statistics(organization_url)

        return jsonify({
            "success": True,
            "data": stats,
            "message": "Template statistics retrieved successfully"
        }), 200

    except Exception as e:
        return jsonify({
            "success": False,
            "error": "Internal server error",
            "details": str(e)
        }), 500


@api_bp.route('/user-journey', methods=['GET'])
@auth_required
def get_user_journey():
    """
    GET API endpoint to retrieve user journey stages.

    Query parameters:
    - product_name (optional): Get journey for specific product

    Returns:
    {
        "success": true,
        "data": [
            {
                "stage": "Awareness",
                "description": "Customer becomes aware of the product",
                "goal": "Generate interest and awareness"
            }
        ]
    }
    """
    try:
        # Get query parameters
        product_name = request.args.get('product_name')

        # Load user journey
        journey = load_user_journey(product_name)

        return jsonify({
            "success": True,
            "data": journey,
            "message": "User journey retrieved successfully"
        }), 200

    except Exception as e:
        return jsonify({
            "success": False,
            "error": "Internal server error",
            "details": str(e)
        }), 500
