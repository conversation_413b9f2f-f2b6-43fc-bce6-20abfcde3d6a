"""
Backend module for organization data processing.

This module compiles all the organization-related functionality from multiple scripts
into a single backend service. It handles URL processing, organization analysis,
and extraction of company information including About Us, What We Do, Domain, and Class.

Functions:
- get_organization_data(org_url): Main function that takes org URL and returns organization details
- scrape_website_content(url): Scrapes content from organization website
- analyze_organization_details(content): Analyzes scraped content to extract organization info
- determine_organization_class(domain): Determines organization class based on domain keywords
"""

import os
import json
import re
import requests
from bs4 import BeautifulSoup
from typing import Dict, Any, Optional
from dotenv import load_dotenv
from openai import OpenAI

# Load environment variables
load_dotenv()


class WebScrapingTool:
    """Tool for scraping web content from organization websites."""
    
    def __init__(self):
        self.name = "Web Scraper"
        self.description = "Scrapes content from a given URL"
    
    def scrape_content(self, url: str) -> str:
        """
        Scrape content from the given URL.
        
        Args:
            url (str): The URL to scrape
            
        Returns:
            str: Scraped text content from the website
        """
        try:
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
            }
            
            response = requests.get(url, headers=headers)
            response.raise_for_status()
            
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # Remove script and style elements
            for script in soup(["script", "style"]):
                script.decompose()
                
            # Get text content
            text = soup.get_text(separator='\n', strip=True)
            
            # Clean up text
            lines = [line.strip() for line in text.splitlines() if line.strip()]
            content = '\n'.join(lines)
            
            return content
            
        except Exception as e:
            return f"Error scraping URL: {str(e)}"


class OrganizationAnalyzerTool:
    """Tool for analyzing organization details from website content."""
    
    def __init__(self):
        self.name = "Organization Analyzer"
        self.description = "Analyzes organization details from website content"
        self.client = OpenAI(api_key=os.getenv("OPENAI_API_KEY"))
    
    def analyze_content(self, content: str) -> Dict[str, Any]:
        """
        Analyze website content to extract organization details.
        
        Args:
            content (str): Scraped website content
            
        Returns:
            Dict[str, Any]: Dictionary containing organization details
        """
        try:
            response = self.client.chat.completions.create(
                model="gpt-4o-mini-2024-07-18",
                messages=[
                    {
                        "role": "system",
                        "content": """You are an organization analyzer. Extract the following details from the provided content:
                        - Business Domain of the organization (Eg:- Edtech, Healthcare etc.)
                        - What We Do
                        - About Us
                        - Class (one of the following: EdTech, E-commerce, Insurance, Banking, Quick-Commerce, TravelTech, Creator Economy Platform, B2B). Food is considered in Quick Commerce and Example of Creator Economy Platform is Substack, Patreon etc.
                        
                        Return the information in a JSON format with these exact keys:
                        {
                            "Domain": "...",
                            "WhatWeDo": "...",
                            "AboutUs": "...",
                            "Class": "..."
                        }
                        
                        If any field is not found, use reasonable defaults based on available information.
                        Never return null or empty values."""
                    },
                    {
                        "role": "user",
                        "content": f"Extract structured organization details from the following content:\n{content}"
                    }
                ],
                temperature=0.9
            )
            
            try:
                json_str = response.choices[0].message.content.strip()
                json_match = re.search(r'\{.*\}', json_str, re.DOTALL)
                if json_match:
                    json_str = json_match.group(0)
                json_output = json.loads(json_str)
                
                default_output = {
                    "Domain": "Unknown Domain",
                    "WhatWeDo": "Information not available",
                    "AboutUs": "Information not available",
                    "Class": "Unknown Class"
                }
                
                for key in default_output:
                    if key not in json_output or not json_output[key]:
                        json_output[key] = default_output[key]
                
                return json_output
                
            except (json.JSONDecodeError, AttributeError) as e:
                return {
                    "Domain": "Error Processing Domain",
                    "WhatWeDo": "Error processing information",
                    "AboutUs": f"Error processing details: {str(e)}",
                    "Class": "Unknown Class"
                }
            
        except Exception as e:
            return {
                "Domain": "Error",
                "WhatWeDo": "Error occurred",
                "AboutUs": f"Failed to analyze organization: {str(e)}",
                "Class": "Unknown Class"
            }


def determine_organization_class(domain: str) -> str:
    """
    Determine organization class based on domain keywords.
    
    Args:
        domain (str): Business domain description
        
    Returns:
        str: Organization class
    """
    domain_lower = domain.lower()
    
    # Mapping of domain keywords to classes
    domain_to_class = {
        "edtech": "EdTech",
        "education": "EdTech",
        "learning": "EdTech",
        "academic": "EdTech",
        "e-commerce": "E-commerce",
        "ecommerce": "E-commerce",
        "retail": "E-commerce",
        "shop": "E-commerce",
        "store": "E-commerce",
        "skincare": "E-commerce",
        "beauty": "E-commerce",
        "wellness": "E-commerce",
        "insurance": "Insurance (InsurTech)",
        "insurtech": "Insurance (InsurTech)",
        "policy": "Insurance (InsurTech)",
        "bank": "Banking",
        "finance": "Banking",
        "fintech": "Banking",
        "food": "Quick-Commerce / Food",
        "delivery": "Quick-Commerce / Food",
        "restaurant": "Quick-Commerce / Food",
        "grocery": "Quick-Commerce / Food",
        "travel": "TravelTech",
        "tourism": "TravelTech",
        "hotel": "TravelTech",
        "flight": "TravelTech",
        "creator": "Creator Economy Platform (e.g., Substack, Patreon)",
        "content": "Creator Economy Platform (e.g., Substack, Patreon)",
        "platform": "Creator Economy Platform (e.g., Substack, Patreon)"
    }
    
    # Try to match domain keywords
    for keyword, class_name in domain_to_class.items():
        if keyword in domain_lower:
            return class_name
    
    # Default fallback
    return "E-commerce"


def scrape_website_content(url: str) -> str:
    """
    Scrape content from organization website.
    
    Args:
        url (str): Organization website URL
        
    Returns:
        str: Scraped website content
    """
    scraper = WebScrapingTool()
    return scraper.scrape_content(url)


def analyze_organization_details(content: str) -> Dict[str, Any]:
    """
    Analyze scraped content to extract organization information.
    
    Args:
        content (str): Scraped website content
        
    Returns:
        Dict[str, Any]: Organization details including Domain, WhatWeDo, AboutUs, Class
    """
    analyzer = OrganizationAnalyzerTool()
    return analyzer.analyze_content(content)


def get_organization_data(org_url: str) -> Dict[str, Any]:
    """
    Main function that takes organization URL and returns complete organization details.

    This function:
    1. Scrapes content from the organization website
    2. Analyzes the content using AI to extract organization details
    3. Determines organization class based on domain keywords
    4. Returns structured organization data

    Args:
        org_url (str): Organization website URL

    Returns:
        Dict[str, Any]: Complete organization data containing:
            - Domain: Business domain of the organization
            - WhatWeDo: Description of what the organization does
            - AboutUs: About us information
            - Class: Organization classification
            - url: Original URL provided
    """
    try:
        print
        # Step 1: Scrape website content
        content = scrape_website_content(org_url)

        if content.startswith("Error scraping URL:"):
            return {
                "Domain": "Error",
                "WhatWeDo": "Could not scrape website content",
                "AboutUs": content,
                "Class": "Unknown Class",
                "url": org_url
            }

        # Step 2: Analyze organization details
        org_data = analyze_organization_details(content)

        # Step 3: Add URL to the organization data
        org_data["url"] = org_url

        # Step 4: Ensure Class field is properly set
        if "Class" not in org_data or not org_data["Class"] or org_data["Class"] == "Unknown Class":
            org_data["Class"] = determine_organization_class(org_data.get("Domain", ""))

        return org_data

    except Exception as e:
        return {
            "Domain": "Error",
            "WhatWeDo": "Error occurred during analysis",
            "AboutUs": f"Failed to analyze organization: {str(e)}",
            "Class": "Unknown Class",
            "url": org_url
        }


# Utility functions for data storage and retrieval
def save_organization_data(org_data: Dict[str, Any], organization_url: Optional[str] = None) -> bool:
    """
    Save organization data to the data storage file.

    Args:
        org_data (Dict[str, Any]): Organization data to save
        organization_url (str, optional): URL of the organization

    Returns:
        bool: True if save successful, False otherwise
    """
    if not org_data:
        return False

    if not organization_url:
        organization_url = org_data.get('url')
        if not organization_url:
            return False

    try:
        from datetime import datetime

        # Ensure org_data has a 'saved' flag
        if 'saved' not in org_data:
            org_data['saved'] = True

        # Load existing data
        all_orgs = {}
        data_file_path = 'data/organization_data.json'

        if os.path.exists(data_file_path):
            try:
                with open(data_file_path, 'r') as f:
                    file_data = json.load(f)
                    if isinstance(file_data, dict):
                        all_orgs = file_data
                    else:
                        # Convert old format to new
                        all_orgs = {}
            except json.JSONDecodeError:
                # If file exists but is invalid, create backup
                if os.path.getsize(data_file_path) > 0:
                    backup_file = f'{data_file_path}.bak.{datetime.now().strftime("%Y%m%d%H%M%S")}'
                    try:
                        import shutil
                        shutil.copy(data_file_path, backup_file)
                        print(f"Created backup of potentially corrupted organization_data.json as {backup_file}")
                    except Exception as backup_err:
                        print(f"Could not create backup: {str(backup_err)}")
                all_orgs = {}

        # Save this organization's data
        all_orgs[organization_url] = org_data

        # Ensure data directory exists
        os.makedirs('data', exist_ok=True)

        # Write back to file
        with open(data_file_path, 'w') as f:
            json.dump(all_orgs, f, indent=2)
        return True
    except Exception as e:
        print(f"Error saving organization data: {str(e)}")
        return False


def load_organization_data(organization_url: Optional[str] = None) -> Dict[str, Any]:
    """
    Load organization data for a specific organization or all organizations.

    Args:
        organization_url (str, optional): URL of the organization to load.
            If None, returns all organizations as a dictionary.

    Returns:
        Dict[str, Any]: Organization data for a specific organization or all organizations
    """
    data_file_path = 'data/organization_data.json'

    try:
        if os.path.exists(data_file_path):
            with open(data_file_path, 'r') as f:
                all_orgs = json.load(f)

                # Handle different data formats
                if not isinstance(all_orgs, dict):
                    all_orgs = {}

                if organization_url:
                    return all_orgs.get(organization_url, {})
                else:
                    return all_orgs
        else:
            return {} if organization_url else {}
    except (FileNotFoundError, json.JSONDecodeError):
        return {} if organization_url else {}


def get_all_organizations() -> list:
    """
    Get a list of all organizations.

    Returns:
        list: List of organization dictionaries
    """
    all_orgs = load_organization_data()
    return [org for _, org in all_orgs.items()]


# Example usage and testing function
def test_organization_analysis(test_url: str = "https://www.analyticsvidhya.com/") -> None:
    """
    Test function to demonstrate organization data extraction.

    Args:
        test_url (str): URL to test with (defaults to Analytics Vidhya)
    """
    print(f"Testing organization analysis for: {test_url}")
    print("-" * 50)

    # Get organization data
    org_data = get_organization_data(test_url)

    # Display results
    print("Organization Analysis Results:")
    print(f"Domain: {org_data.get('Domain', 'N/A')}")
    print(f"What We Do: {org_data.get('WhatWeDo', 'N/A')}")
    print(f"About Us: {org_data.get('AboutUs', 'N/A')}")
    print(f"Class: {org_data.get('Class', 'N/A')}")
    print(f"URL: {org_data.get('url', 'N/A')}")

    # Save the data
    if save_organization_data(org_data, test_url):
        print("\n✅ Organization data saved successfully!")
    else:
        print("\n❌ Failed to save organization data!")


if __name__ == "__main__":
    # Test the module
    test_organization_analysis()
